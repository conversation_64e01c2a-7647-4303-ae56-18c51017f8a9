# Spring Boot Netty WebSocket Demo

This project demonstrates a Spring Boot application with WebSocket support using <PERSON><PERSON> as the underlying server. It provides both echo functionality and server-side broadcasting capabilities.

## Features

- **WebSocket Echo**: Messages sent by clients are echoed back with a "Echo from server:" prefix
- **Server Broadcasting**: Server automatically sends periodic updates to all connected clients every 5 seconds
- **Session Management**: Tracks and manages multiple concurrent WebSocket connections
- **Web Client**: Simple HTML/JavaScript client for testing WebSocket functionality

## Project Structure

```
src/
├── main/
│   ├── java/com/example/mud/
│   │   ├── MudApplication.java                    # Main Spring Boot application
│   │   └── websocket/
│   │       ├── WebSocketConfig.java               # WebSocket configuration
│   │       ├── EchoAndBroadcastWebSocketHandler.java  # WebSocket message handler
│   │       └── WebSocketSessionManager.java       # Session management
│   └── resources/
│       ├── application.properties                 # Application configuration
│       └── static/
│           ├── index.html                        # Web client interface
│           └── websocket-client.js               # WebSocket client JavaScript
└── test/                                         # Test files (to be added)
```

## Technologies Used

- **Spring Boot 3.2.0**: Main framework
- **Spring WebFlux**: Reactive web framework
- **Netty**: High-performance NIO server
- **Spring WebSocket**: WebSocket support
- **Project Reactor**: Reactive streams implementation

## Getting Started

### Prerequisites

- Java 17 or higher
- Maven 3.6 or higher

### Running the Application

1. **Clone or navigate to the project directory**

2. **Build the project**:
   ```bash
   mvn clean compile
   ```

3. **Run the application**:
   ```bash
   mvn spring-boot:run
   ```

4. **Access the web client**:
   Open your browser and navigate to: `http://localhost:8080`

### Testing the WebSocket Connection

1. **Using the Web Client**:
   - Open `http://localhost:8080` in your browser
   - Click "Connect" to establish a WebSocket connection
   - Type messages in the input field and click "Send"
   - Observe both echo responses and periodic server broadcasts

2. **Using WebSocket Testing Tools**:
   - Connect to: `ws://localhost:8080/websocket-echo`
   - Send any text message to receive an echo response
   - Observe periodic server broadcasts every 5 seconds

## WebSocket Endpoints

- **`/websocket-echo`**: Main WebSocket endpoint for echo and broadcast functionality

## Configuration

The application can be configured through `src/main/resources/application.properties`:

- `server.port`: Server port (default: 8080)
- `logging.level.com.example.mud`: Logging level for application classes
- `logging.level.org.springframework.web.socket`: WebSocket logging level

## How It Works

1. **Connection**: When a client connects to `/websocket-echo`, a new WebSocket session is created and managed
2. **Echo**: Any message sent by a client is immediately echoed back with a prefix
3. **Broadcasting**: A scheduled task runs every 5 seconds, sending timestamp updates to all connected clients
4. **Session Management**: The `WebSocketSessionManager` tracks all active connections and handles broadcasting

## Development

### Adding New Features

- Modify `EchoAndBroadcastWebSocketHandler` to add new message handling logic
- Update `WebSocketSessionManager` to add new session management features
- Extend the web client in `index.html` and `websocket-client.js` for new UI features

### Testing

To run tests (when implemented):
```bash
mvn test
```

## Troubleshooting

- **Connection Issues**: Ensure the server is running on port 8080 and no firewall is blocking the connection
- **CORS Issues**: The configuration allows all origins (`*`) for development; restrict this in production
- **Logging**: Enable DEBUG logging for WebSocket classes to troubleshoot connection issues

## Production Considerations

- Remove `setAllowedOrigins("*")` and specify allowed origins explicitly
- Add authentication and authorization for WebSocket connections
- Implement proper error handling and reconnection logic
- Add monitoring and metrics for WebSocket connections
- Consider using a message broker for scaling across multiple instances
