package com.example.mud.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.reactive.handler.SimpleUrlHandlerMapping;
import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.server.WebSocketService;
import org.springframework.web.reactive.socket.server.support.HandshakeWebSocketService;
import org.springframework.web.reactive.socket.server.support.WebSocketHandlerAdapter;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Configuration
@EnableWebSocket // Enables WebSocket capabilities
public class WebSocketConfig implements WebSocketConfigurer {

    // Store active WebSocket sessions to send messages to all clients
    private final WebSocketSessionManager sessionManager = new WebSocketSessionManager();

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        // Register our custom WebSocketHandler for the "/websocket-echo" endpoint
        registry.addHandler(webSocketHandler(), "/websocket-echo").setAllowedOrigins("*"); // Allow all origins for simplicity
    }

    @Bean
    public WebSocketHandler webSocketHandler() {
        // Return an instance of our custom WebSocketHandler
        return new EchoAndBroadcastWebSocketHandler(sessionManager);
    }

    // These beans are necessary for Spring WebFlux to correctly handle WebSockets
    @Bean
    public WebSocketHandlerAdapter handlerAdapter() {
        return new WebSocketHandlerAdapter(webSocketService());
    }

    @Bean
    public WebSocketService webSocketService() {
        // Use HandshakeWebSocketService for standard WebSocket handshake
        return new HandshakeWebSocketService();
    }

    @Bean
    public SimpleUrlHandlerMapping webSocketMapping(WebSocketHandler webSocketHandler) {
        Map<String, WebSocketHandler> map = new HashMap<>();
        map.put("/websocket-echo", webSocketHandler);

        SimpleUrlHandlerMapping mapping = new SimpleUrlHandlerMapping();
        mapping.setUrlMap(map);
        mapping.setOrder(10); // Ensure this mapping is processed before others
        return mapping;
    }

    @Bean
    public ScheduledExecutorService scheduledExecutorService() {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        // Schedule a task to send a message to all clients every 5 seconds
        scheduler.scheduleAtFixedRate(() -> {
            String message = "Server Update: It's " + System.currentTimeMillis() / 1000 + " seconds since epoch!";
            sessionManager.broadcast(message);
        }, 5, 5, TimeUnit.SECONDS);
        return scheduler;
    }
}
