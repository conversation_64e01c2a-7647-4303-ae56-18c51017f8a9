package com.example.mud;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

@SpringBootApplication
@ComponentScan(basePackages = "com.example.mud") // Ensure Spring scans your WebSocket config
public class MudApplication {

    public static void main(String[] args) {
        SpringApplication.run(MudApplication.class, args);
    }
}
