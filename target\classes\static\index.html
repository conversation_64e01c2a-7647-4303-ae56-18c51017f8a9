<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Demo</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 20px;
            margin: 10px 0;
        }
        .messages {
            height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
            margin: 10px 0;
        }
        .message {
            margin: 5px 0;
            padding: 5px;
            border-radius: 3px;
        }
        .echo {
            background-color: #e7f3ff;
        }
        .broadcast {
            background-color: #fff3cd;
        }
        .status {
            background-color: #d4edda;
        }
        .error {
            background-color: #f8d7da;
        }
        input[type="text"] {
            width: 70%;
            padding: 8px;
            margin: 5px;
        }
        button {
            padding: 8px 15px;
            margin: 5px;
            cursor: pointer;
        }
        .connected {
            color: green;
        }
        .disconnected {
            color: red;
        }
    </style>
</head>
<body>
    <h1>Spring Boot Netty WebSocket Demo</h1>
    
    <div class="container">
        <h2>Connection Status</h2>
        <p>Status: <span id="status" class="disconnected">Disconnected</span></p>
        <button id="connectBtn" onclick="connect()">Connect</button>
        <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
    </div>

    <div class="container">
        <h2>Send Message</h2>
        <input type="text" id="messageInput" placeholder="Enter your message here..." disabled>
        <button id="sendBtn" onclick="sendMessage()" disabled>Send</button>
    </div>

    <div class="container">
        <h2>Messages</h2>
        <div id="messages" class="messages"></div>
        <button onclick="clearMessages()">Clear Messages</button>
    </div>

    <script src="websocket-client.js"></script>
</body>
</html>
