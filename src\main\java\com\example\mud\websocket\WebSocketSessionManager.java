package com.example.mud.websocket;

import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class WebSocketSessionManager {

    // Store active sessions by ID
    private final Map<String, WebSocketSession> sessions = new ConcurrentHashMap<>();

    // A shared sink to broadcast messages to all connected clients
    // Sinks.many().multicast().onBackpressureBuffer() is suitable for broadcasting
    private final Sinks.Many<String> broadcastSink = Sinks.many().multicast().onBackpressureBuffer();

    public void addSession(WebSocketSession session) {
        sessions.put(session.getId(), session);
    }

    public void removeSession(String sessionId) {
        sessions.remove(sessionId);
    }

    // Method to broadcast a message to all connected clients
    public void broadcast(String message) {
        System.out.println("Broadcasting: " + message);
        // Emit the message to the sink, which will then be consumed by all listening sessions
        broadcastSink.tryEmitNext(message);
    }

    // Get the sink for <PERSON>SocketHandler to subscribe to
    public Sinks.Many<String> getBroadcastSink() {
        return broadcastSink;
    }
}
