package com.example.mud.websocket;

import org.springframework.web.reactive.socket.WebSocketHandler;
import org.springframework.web.reactive.socket.WebSocketMessage;
import org.springframework.web.reactive.socket.WebSocketSession;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class EchoAndBroadcastWebSocketHandler implements WebSocketHandler {

    private final WebSocketSessionManager sessionManager;

    public EchoAndBroadcastWebSocketHandler(WebSocketSessionManager sessionManager) {
        this.sessionManager = sessionManager;
    }

    @Override
    public Mono<Void> handle(WebSocketSession session) {
        // Add the session to our manager when it connects
        sessionManager.addSession(session);
        System.out.println("New WebSocket session opened: " + session.getId());

        // Handle incoming messages (echo them back)
        Mono<Void> input = session.receive()
            .doOnNext(message -> {
                String payload = message.getPayloadAsText();
                System.out.println("Received from " + session.getId() + ": " + payload);
                // Echo message back to the sender
                session.send(Mono.just(session.textMessage("Echo from server: " + payload)))
                       .subscribe(); // Subscribe to send the message
            })
            .then(); // Complete the input stream

        // Handle outgoing messages (from server-side broadcasts)
        Mono<Void> output = session.send(
            sessionManager.getBroadcastSink() // Get the sink for broadcasting messages
                .asFlux() // Convert to a Flux to send multiple messages
                .map(session::textMessage) // Map String messages to WebSocketMessage
        );

        // When the session closes, remove it from the manager
        session.getHandshakeInfo().getPrincipal().subscribe(
            principal -> System.out.println("WebSocket session closed for principal: " + principal.getName()),
            error -> System.err.println("Error getting principal for session " + session.getId() + ": " + error.getMessage()),
            () -> {
                sessionManager.removeSession(session.getId());
                System.out.println("WebSocket session closed: " + session.getId());
            }
        );

        return Mono.zip(input, output).then(); // Combine input and output handling
    }
}
