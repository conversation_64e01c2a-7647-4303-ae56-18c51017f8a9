let websocket = null;
let isConnected = false;

// DOM elements
const statusElement = document.getElementById('status');
const connectBtn = document.getElementById('connectBtn');
const disconnectBtn = document.getElementById('disconnectBtn');
const messageInput = document.getElementById('messageInput');
const sendBtn = document.getElementById('sendBtn');
const messagesDiv = document.getElementById('messages');

// WebSocket connection
function connect() {
    if (websocket !== null) {
        return;
    }

    const wsUrl = `ws://localhost:8080/websocket-echo`;
    websocket = new WebSocket(wsUrl);

    websocket.onopen = function(event) {
        console.log('WebSocket connected');
        isConnected = true;
        updateUI();
        addMessage('Connected to WebSocket server', 'status');
    };

    websocket.onmessage = function(event) {
        console.log('Message received:', event.data);
        
        // Determine message type based on content
        if (event.data.startsWith('Echo from server:')) {
            addMessage(event.data, 'echo');
        } else if (event.data.startsWith('Server Update:')) {
            addMessage(event.data, 'broadcast');
        } else {
            addMessage(event.data, 'message');
        }
    };

    websocket.onclose = function(event) {
        console.log('WebSocket disconnected');
        isConnected = false;
        websocket = null;
        updateUI();
        addMessage('Disconnected from WebSocket server', 'status');
    };

    websocket.onerror = function(error) {
        console.error('WebSocket error:', error);
        addMessage('WebSocket error occurred', 'error');
    };
}

function disconnect() {
    if (websocket !== null) {
        websocket.close();
    }
}

function sendMessage() {
    const message = messageInput.value.trim();
    if (message && websocket && isConnected) {
        websocket.send(message);
        addMessage(`Sent: ${message}`, 'message');
        messageInput.value = '';
    }
}

function addMessage(message, type = 'message') {
    const messageElement = document.createElement('div');
    messageElement.className = `message ${type}`;
    messageElement.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    messagesDiv.appendChild(messageElement);
    messagesDiv.scrollTop = messagesDiv.scrollHeight;
}

function clearMessages() {
    messagesDiv.innerHTML = '';
}

function updateUI() {
    if (isConnected) {
        statusElement.textContent = 'Connected';
        statusElement.className = 'connected';
        connectBtn.disabled = true;
        disconnectBtn.disabled = false;
        messageInput.disabled = false;
        sendBtn.disabled = false;
    } else {
        statusElement.textContent = 'Disconnected';
        statusElement.className = 'disconnected';
        connectBtn.disabled = false;
        disconnectBtn.disabled = true;
        messageInput.disabled = true;
        sendBtn.disabled = true;
    }
}

// Allow sending messages with Enter key
messageInput.addEventListener('keypress', function(event) {
    if (event.key === 'Enter') {
        sendMessage();
    }
});

// Initialize UI
updateUI();
